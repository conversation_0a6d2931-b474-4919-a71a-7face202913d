#!/usr/bin/env python3
"""
Telegram GUI Downloader
A modern GUI application for downloading content from Telegram channels and groups.
"""

import sys
import asyncio
import logging
from pathlib import Path

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer
from PySide6.QtGui import QIcon

from src.gui.main_window import MainWindow
from src.core.config import Config
from src.core.logger import setup_logging


def main():
    """Main entry point for the application."""
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # Create Qt application
    app = QApplication(sys.argv)
    app.setApplicationName("Telegram GUI Downloader")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("TG-GUI-DL")
    
    # Load configuration
    config = Config()
    
    # Create main window
    main_window = MainWindow(config)
    main_window.show()
    
    logger.info("Application started successfully")
    
    # Run the application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
