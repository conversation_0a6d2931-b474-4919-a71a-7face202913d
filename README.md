# Telegram GUI Downloader

A modern GUI application for downloading content from Telegram channels and groups using PySide6 and Pyrogram.

## Features

- 🔐 Login via API ID/Hash + phone number or bot token
- 📋 Browse all available channels, groups, and chats
- 📥 Download specific messages or bulk download from channels/groups
- 🎯 Advanced filtering and search functionality
- 📊 Progress tracking for downloads
- ⚙️ Configurable settings and export formats

## Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Get your Telegram API credentials:
   - Go to https://my.telegram.org/apps
   - Create a new application
   - Copy your API ID and API Hash

3. Create a `.env` file from `.env.example`:
```bash
cp .env.example .env
```

4. Edit `.env` and add your credentials

5. Run the application:
```bash
python main.py
```

## Requirements

- Python 3.8+
- PySide6
- Pyrogram
- Valid Telegram API credentials

## License

MIT License
