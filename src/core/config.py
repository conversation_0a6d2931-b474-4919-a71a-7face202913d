"""Configuration management for the Telegram GUI Downloader."""

import os
from pathlib import Path
from typing import Optional
from dotenv import load_dotenv


class Config:
    """Application configuration manager."""
    
    def __init__(self):
        """Initialize configuration by loading from .env file."""
        # Load environment variables from .env file
        env_path = Path(".env")
        if env_path.exists():
            load_dotenv(env_path)
        
        # Telegram API settings
        self.api_id: Optional[str] = os.getenv("API_ID")
        self.api_hash: Optional[str] = os.getenv("API_HASH")
        self.bot_token: Optional[str] = os.getenv("BOT_TOKEN")
        
        # Application settings
        self.download_path = Path(os.getenv("DOWNLOAD_PATH", "./downloads"))
        self.session_name = "telegram_gui_session"
        
        # GUI settings
        self.window_width = 1200
        self.window_height = 800
        self.theme = "light"  # light or dark
        
        # Download settings
        self.max_concurrent_downloads = 3
        self.chunk_size = 1024 * 1024  # 1MB chunks
        
        # Ensure download directory exists
        self.download_path.mkdir(parents=True, exist_ok=True)
    
    def is_configured(self) -> bool:
        """Check if the basic configuration is present."""
        return bool(self.api_id and self.api_hash)
    
    def has_bot_token(self) -> bool:
        """Check if bot token is configured."""
        return bool(self.bot_token)
    
    def save_credentials(self, api_id: str, api_hash: str, bot_token: str = None):
        """Save credentials to .env file."""
        env_path = Path(".env")
        
        # Read existing content
        existing_content = ""
        if env_path.exists():
            existing_content = env_path.read_text()
        
        # Update or add credentials
        lines = existing_content.split('\n')
        updated_lines = []
        
        # Track which values we've updated
        updated_keys = set()
        
        for line in lines:
            if line.startswith('API_ID='):
                updated_lines.append(f'API_ID={api_id}')
                updated_keys.add('API_ID')
            elif line.startswith('API_HASH='):
                updated_lines.append(f'API_HASH={api_hash}')
                updated_keys.add('API_HASH')
            elif line.startswith('BOT_TOKEN=') and bot_token:
                updated_lines.append(f'BOT_TOKEN={bot_token}')
                updated_keys.add('BOT_TOKEN')
            else:
                updated_lines.append(line)
        
        # Add missing keys
        if 'API_ID' not in updated_keys:
            updated_lines.append(f'API_ID={api_id}')
        if 'API_HASH' not in updated_keys:
            updated_lines.append(f'API_HASH={api_hash}')
        if bot_token and 'BOT_TOKEN' not in updated_keys:
            updated_lines.append(f'BOT_TOKEN={bot_token}')
        
        # Write back to file
        env_path.write_text('\n'.join(updated_lines))
        
        # Update current instance
        self.api_id = api_id
        self.api_hash = api_hash
        if bot_token:
            self.bot_token = bot_token
