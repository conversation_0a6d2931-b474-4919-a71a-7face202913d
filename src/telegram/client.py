"""Telegram client wrapper using Pyrogram."""

import asyncio
import logging
from typing import Optional, List, Dict, Any, Callable
from pathlib import Path

from pyrogram import Client, types
from pyrogram.errors import (
    SessionPasswordNeeded, PhoneCodeInvalid, PhoneCodeExpired,
    PhoneNumberInvalid, ApiIdInvalid, AccessTokenInvalid
)

from ..core.config import Config


class TelegramClient:
    """Wrapper around Pyrogram client with authentication and session management."""
    
    def __init__(self, config: Config):
        """Initialize the Telegram client."""
        self.config = config
        self.client: Optional[Client] = None
        self.logger = logging.getLogger(__name__)
        self.is_authenticated = False
        self.user_info: Optional[types.User] = None
        
        # Authentication callbacks
        self.phone_code_callback: Optional[Callable[[str], str]] = None
        self.password_callback: Optional[Callable[[], str]] = None
        
    def set_auth_callbacks(self, 
                          phone_code_callback: Callable[[str], str],
                          password_callback: Callable[[], str]):
        """Set callbacks for authentication prompts."""
        self.phone_code_callback = phone_code_callback
        self.password_callback = password_callback
    
    async def authenticate_user(self, phone_number: str) -> bool:
        """Authenticate using phone number and API credentials."""
        try:
            if not self.config.api_id or not self.config.api_hash:
                raise ValueError("API ID and API Hash are required")
            
            # Create client
            self.client = Client(
                name=self.config.session_name,
                api_id=int(self.config.api_id),
                api_hash=self.config.api_hash,
                phone_number=phone_number,
                workdir="sessions"
            )
            
            # Ensure sessions directory exists
            Path("sessions").mkdir(exist_ok=True)
            
            await self.client.start()
            # Record the running loop used by the client so other threads can dispatch to it
            try:
                self.loop = asyncio.get_running_loop()
            except RuntimeError:
                self.loop = None
            
            # Get user info
            self.user_info = await self.client.get_me()
            self.is_authenticated = True
            
            self.logger.info(f"Successfully authenticated as {self.user_info.first_name}")
            return True
            
        except SessionPasswordNeeded:
            if not self.password_callback:
                raise ValueError("Two-factor authentication required but no password callback set")
            
            password = self.password_callback()
            await self.client.check_password(password)
            
            # Record the running loop used by the client so other threads can dispatch to it
            try:
                self.loop = asyncio.get_running_loop()
            except RuntimeError:
                self.loop = None
            
            self.user_info = await self.client.get_me()
            self.is_authenticated = True
            return True
            
        except (ApiIdInvalid, ValueError) as e:
            self.logger.error(f"Invalid API credentials: {e}")
            return False
            
        except Exception as e:
            self.logger.error(f"Authentication failed: {e}")
            return False
    
    async def authenticate_bot(self, bot_token: str) -> bool:
        """Authenticate using bot token."""
        try:
            if not self.config.api_id or not self.config.api_hash:
                raise ValueError("API ID and API Hash are required")
            
            # Create client for bot
            self.client = Client(
                name=f"{self.config.session_name}_bot",
                api_id=int(self.config.api_id),
                api_hash=self.config.api_hash,
                bot_token=bot_token,
                workdir="sessions"
            )
            
            # Ensure sessions directory exists
            Path("sessions").mkdir(exist_ok=True)
            
            await self.client.start()
            # Record the running loop used by the client so other threads can dispatch to it
            try:
                self.loop = asyncio.get_running_loop()
            except RuntimeError:
                self.loop = None
            
            # Get bot info
            self.user_info = await self.client.get_me()
            self.is_authenticated = True
            
            self.logger.info(f"Successfully authenticated bot: {self.user_info.username}")
            return True
            
        except AccessTokenInvalid:
            self.logger.error("Invalid bot token")
            return False
            
        except Exception as e:
            self.logger.error(f"Bot authentication failed: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from Telegram."""
        if self.client and self.client.is_connected:
            await self.client.stop()
            self.is_authenticated = False
            self.user_info = None
            self.logger.info("Disconnected from Telegram")
    
    async def get_dialogs(self) -> List[types.Dialog]:
        """Get all dialogs (chats, channels, groups)."""
        if not self.is_authenticated or not self.client:
            raise RuntimeError("Client not authenticated")
        
        dialogs = []
        async for dialog in self.client.get_dialogs():
            dialogs.append(dialog)
        
        return dialogs
    
    async def get_chat_info(self, chat_id: int) -> types.Chat:
        """Get information about a specific chat."""
        if not self.is_authenticated or not self.client:
            raise RuntimeError("Client not authenticated")
        
        return await self.client.get_chat(chat_id)
    
    async def get_chat_history(self, chat_id: int, limit: int = 100) -> List[types.Message]:
        """Get message history from a chat."""
        if not self.is_authenticated or not self.client:
            raise RuntimeError("Client not authenticated")
        
        messages = []
        async for message in self.client.get_chat_history(chat_id, limit=limit):
            messages.append(message)
        
        return messages
    
    async def download_media(self, message: types.Message, 
                           file_path: str, 
                           progress_callback: Optional[Callable[[int, int], None]] = None) -> str:
        """Download media from a message."""
        if not self.is_authenticated or not self.client:
            raise RuntimeError("Client not authenticated")
        
        if not message.media:
            raise ValueError("Message has no media to download")
        
        return await self.client.download_media(
            message,
            file_name=file_path,
            progress=progress_callback
        )
