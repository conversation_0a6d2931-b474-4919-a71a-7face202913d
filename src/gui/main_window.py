"""Main window for the Telegram GUI Downloader."""

import asyncio
import logging
from typing import Optional

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QSplitter, QStatusBar, QMenuBar, QToolBar,
    QMessageBox, QProgressBar, QLabel
)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QAction, QIcon

from ..core.config import Config
from ..telegram.client import TelegramClient
from .auth_dialog import AuthDialog
from .chat_list import ChatListWidget
from .content_browser import ContentBrowserWidget
from .download_manager import DownloadManagerWidget


class MainWindow(QMainWindow):
    """Main application window."""
    
    # Signals
    authentication_changed = Signal(bool)
    
    def __init__(self, config: Config):
        """Initialize the main window."""
        super().__init__()
        
        self.config = config
        self.telegram_client = TelegramClient(config)
        self.logger = logging.getLogger(__name__)
        
        # Setup UI
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()
        
        # Connect signals
        self.authentication_changed.connect(self.on_authentication_changed)
        
        # Check if we need to authenticate
        QTimer.singleShot(100, self.check_authentication)
    
    def setup_ui(self):
        """Setup the main UI layout."""
        self.setWindowTitle("Telegram GUI Downloader")
        self.setGeometry(100, 100, self.config.window_width, self.config.window_height)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        
        # Create splitter for resizable panels
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # Left panel - Chat list
        self.chat_list = ChatListWidget(self.telegram_client)
        self.chat_list.setMinimumWidth(300)
        self.chat_list.setMaximumWidth(400)
        splitter.addWidget(self.chat_list)
        
        # Right panel - Content browser and download manager
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # Content browser
        self.content_browser = ContentBrowserWidget(self.telegram_client)
        right_layout.addWidget(self.content_browser)
        
        # Download manager
        self.download_manager = DownloadManagerWidget(self.telegram_client, self.config)
        self.download_manager.setMaximumHeight(200)
        right_layout.addWidget(self.download_manager)
        
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([300, 900])
        
        # Connect widgets
        self.chat_list.chat_selected.connect(self.content_browser.load_chat)
        self.content_browser.download_requested.connect(self.download_manager.add_download)
    
    def setup_menu_bar(self):
        """Setup the menu bar."""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("&File")
        
        # Login action
        self.login_action = QAction("&Login", self)
        self.login_action.setShortcut("Ctrl+L")
        self.login_action.triggered.connect(self.show_auth_dialog)
        file_menu.addAction(self.login_action)
        
        # Logout action
        self.logout_action = QAction("Log&out", self)
        self.logout_action.triggered.connect(self.logout)
        self.logout_action.setEnabled(False)
        file_menu.addAction(self.logout_action)
        
        file_menu.addSeparator()
        
        # Exit action
        exit_action = QAction("E&xit", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Settings menu
        settings_menu = menubar.addMenu("&Settings")
        
        # Preferences action
        preferences_action = QAction("&Preferences", self)
        preferences_action.triggered.connect(self.show_preferences)
        settings_menu.addAction(preferences_action)
        
        # Help menu
        help_menu = menubar.addMenu("&Help")
        
        # About action
        about_action = QAction("&About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """Setup the toolbar."""
        toolbar = QToolBar("Main Toolbar")
        self.addToolBar(toolbar)
        
        # Login/Logout button
        self.auth_action = QAction("Login", self)
        self.auth_action.triggered.connect(self.toggle_authentication)
        toolbar.addAction(self.auth_action)
        
        toolbar.addSeparator()
        
        # Refresh button
        refresh_action = QAction("Refresh", self)
        refresh_action.triggered.connect(self.refresh_data)
        toolbar.addAction(refresh_action)
    
    def setup_status_bar(self):
        """Setup the status bar."""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Status label
        self.status_label = QLabel("Not connected")
        self.status_bar.addWidget(self.status_label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
    
    def check_authentication(self):
        """Check if we need to authenticate."""
        if not self.config.is_configured():
            self.show_auth_dialog()
        else:
            # Try to connect with existing session
            self.status_label.setText("Checking existing session...")
            # This would be implemented with async handling
    
    def show_auth_dialog(self):
        """Show the authentication dialog."""
        # Pass the existing TelegramClient so the authenticated state and user_info are shared
        dialog = AuthDialog(self.config, self, telegram_client=self.telegram_client)
        if dialog.exec() == AuthDialog.Accepted:
            # Authentication successful
            self.authentication_changed.emit(True)
    
    def logout(self):
        """Logout from Telegram."""
        asyncio.create_task(self.telegram_client.disconnect())
        self.authentication_changed.emit(False)
    
    def toggle_authentication(self):
        """Toggle authentication state."""
        if self.telegram_client.is_authenticated:
            self.logout()
        else:
            self.show_auth_dialog()
    
    def on_authentication_changed(self, authenticated: bool):
        """Handle authentication state changes."""
        self.login_action.setEnabled(not authenticated)
        self.logout_action.setEnabled(authenticated)
        
        if authenticated:
            self.auth_action.setText("Logout")
            # Safely handle cases where user_info might not yet be populated
            if self.telegram_client.user_info is not None:
                name = self.telegram_client.user_info.first_name or self.telegram_client.user_info.username or "User"
            else:
                name = "User"
            self.status_label.setText(f"Connected as {name}")
            self.chat_list.load_chats()
        else:
            self.auth_action.setText("Login")
            self.status_label.setText("Not connected")
            self.chat_list.clear()
            self.content_browser.clear()
    
    def refresh_data(self):
        """Refresh all data."""
        if self.telegram_client.is_authenticated:
            self.chat_list.load_chats()
    
    def show_preferences(self):
        """Show preferences dialog."""
        QMessageBox.information(self, "Preferences", "Preferences dialog not implemented yet.")
    
    def show_about(self):
        """Show about dialog."""
        QMessageBox.about(
            self, 
            "About Telegram GUI Downloader",
            "Telegram GUI Downloader v1.0.0\n\n"
            "A modern GUI application for downloading content from Telegram channels and groups.\n\n"
            "Built with PySide6 and Pyrogram."
        )
    
    def closeEvent(self, event):
        """Handle window close event."""
        if self.telegram_client.is_authenticated:
            asyncio.create_task(self.telegram_client.disconnect())
        event.accept()
