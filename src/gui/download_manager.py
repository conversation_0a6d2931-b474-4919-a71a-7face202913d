"""Download manager widget for handling file downloads from Telegram."""

import asyncio
import logging
import os
from pathlib import Path
from typing import List, Optional, Union
from datetime import datetime

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
    QLabel, QPushButton, QProgressBar, QTextEdit, QFileDialog,
    QMessageBox, QComboBox
)
from PySide6.QtCore import Qt, Signal, QThread
from PySide6.QtGui import QFont

from pyrogram import types
from ..telegram.client import TelegramClient
from ..core.config import Config


class DownloadWorker(QThread):
    """Worker thread for downloading files."""
    
    # Signals
    download_progress = Signal(int, int, str)  # current, total, filename
    download_completed = Signal(str, str)  # filename, filepath
    download_failed = Signal(str, str)  # filename, error
    all_downloads_completed = Signal()
    
    def __init__(self, client: TelegramClient, messages: List[types.Message], download_path: Path):
        super().__init__()
        self.client = client
        self.messages = messages
        self.download_path = download_path
        self.is_cancelled = False
    
    def cancel(self):
        """Cancel the download process."""
        self.is_cancelled = True
    
    def run(self):
        """Run the download process."""
        try:
            # Ensure download directory exists
            self.download_path.mkdir(parents=True, exist_ok=True)
            
            for i, message in enumerate(self.messages):
                if self.is_cancelled:
                    break
                
                if not message.media:
                    continue
                
                # Generate filename
                filename = self.generate_filename(message)
                filepath = self.download_path / filename
                
                try:
                    # Download the file using the client's loop if available
                    if hasattr(self.client, "loop") and self.client.loop is not None:
                        future = asyncio.run_coroutine_threadsafe(
                            self.client.download_media(
                                message,
                                str(filepath),
                                progress_callback=lambda current, total: self.download_progress.emit(
                                    current, total, filename
                                )
                            ),
                            self.client.loop,
                        )
                        downloaded_path = future.result()
                    else:
                        loop = asyncio.new_event_loop()
                        try:
                            asyncio.set_event_loop(loop)
                            downloaded_path = loop.run_until_complete(
                                self.client.download_media(
                                    message,
                                    str(filepath),
                                    progress_callback=lambda current, total: self.download_progress.emit(
                                        current, total, filename
                                    )
                                )
                            )
                        finally:
                            loop.close()
                    
                    if downloaded_path:
                        self.download_completed.emit(filename, downloaded_path)
                    else:
                        self.download_failed.emit(filename, "Download returned None")
                        
                except Exception as e:
                    self.download_failed.emit(filename, str(e))
            
            if not self.is_cancelled:
                self.all_downloads_completed.emit()
                
        except Exception as e:
            self.download_failed.emit("General", str(e))
    
    def generate_filename(self, message: types.Message) -> str:
        """Generate a filename for the message media."""
        timestamp = message.date.strftime("%Y%m%d_%H%M%S") if message.date else "unknown"
        
        if message.document and message.document.file_name:
            # Use original filename if available
            base_name = message.document.file_name
            name, ext = os.path.splitext(base_name)
            return f"{timestamp}_{name}{ext}"
        elif message.photo:
            return f"{timestamp}_photo.jpg"
        elif message.video:
            return f"{timestamp}_video.mp4"
        elif message.audio:
            return f"{timestamp}_audio.mp3"
        elif message.voice:
            return f"{timestamp}_voice.ogg"
        elif message.sticker:
            return f"{timestamp}_sticker.webp"
        elif message.animation:
            return f"{timestamp}_animation.gif"
        else:
            return f"{timestamp}_media"


class DownloadItem(QListWidgetItem):
    """Custom list item for download display."""
    
    def __init__(self, filename: str, status: str = "Pending"):
        super().__init__()
        self.filename = filename
        self.status = status
        self.progress = 0
        self.total_size = 0
        self.error_message = ""
        
        self.update_display()
    
    def update_display(self):
        """Update the display text."""
        if self.status == "Downloading" and self.total_size > 0:
            progress_percent = (self.progress / self.total_size) * 100
            size_mb = self.total_size / (1024 * 1024)
            text = f"{self.filename} - {progress_percent:.1f}% ({size_mb:.1f} MB)"
        elif self.status == "Completed":
            text = f"{self.filename} - ✓ Completed"
        elif self.status == "Failed":
            text = f"{self.filename} - ✗ Failed: {self.error_message}"
        else:
            text = f"{self.filename} - {self.status}"
        
        self.setText(text)
    
    def set_progress(self, current: int, total: int):
        """Update download progress."""
        self.progress = current
        self.total_size = total
        self.status = "Downloading"
        self.update_display()
    
    def set_completed(self, filepath: str):
        """Mark as completed."""
        self.status = "Completed"
        self.filepath = filepath
        self.update_display()
    
    def set_failed(self, error: str):
        """Mark as failed."""
        self.status = "Failed"
        self.error_message = error
        self.update_display()


class DownloadManagerWidget(QWidget):
    """Widget for managing file downloads."""
    
    def __init__(self, telegram_client: TelegramClient, config: Config):
        super().__init__()
        self.telegram_client = telegram_client
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.download_worker: Optional[DownloadWorker] = None
        self.download_items = {}  # filename -> DownloadItem
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the widget UI."""
        layout = QVBoxLayout(self)
        
        # Header
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Download Manager")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Download path selection
        self.path_label = QLabel(f"Path: {self.config.download_path}")
        header_layout.addWidget(self.path_label)
        
        self.browse_button = QPushButton("Browse")
        self.browse_button.clicked.connect(self.browse_download_path)
        header_layout.addWidget(self.browse_button)
        
        layout.addLayout(header_layout)
        
        # Controls
        controls_layout = QHBoxLayout()
        
        # Download format/quality options (placeholder for future enhancement)
        controls_layout.addWidget(QLabel("Quality:"))
        self.quality_combo = QComboBox()
        self.quality_combo.addItems(["Original", "High", "Medium", "Low"])
        controls_layout.addWidget(self.quality_combo)
        
        controls_layout.addStretch()
        
        # Control buttons
        self.pause_button = QPushButton("Pause")
        self.pause_button.clicked.connect(self.pause_downloads)
        self.pause_button.setEnabled(False)
        controls_layout.addWidget(self.pause_button)
        
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.cancel_downloads)
        self.cancel_button.setEnabled(False)
        controls_layout.addWidget(self.cancel_button)
        
        self.clear_button = QPushButton("Clear")
        self.clear_button.clicked.connect(self.clear_downloads)
        controls_layout.addWidget(self.clear_button)
        
        layout.addLayout(controls_layout)
        
        # Overall progress
        self.overall_progress = QProgressBar()
        self.overall_progress.setVisible(False)
        layout.addWidget(self.overall_progress)
        
        # Download list
        self.download_list = QListWidget()
        layout.addWidget(self.download_list)
        
        # Status label
        self.status_label = QLabel("No downloads")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: gray;")
        layout.addWidget(self.status_label)
    
    def browse_download_path(self):
        """Browse for download directory."""
        path = QFileDialog.getExistingDirectory(
            self, 
            "Select Download Directory", 
            str(self.config.download_path)
        )
        
        if path:
            self.config.download_path = Path(path)
            self.path_label.setText(f"Path: {self.config.download_path}")
    
    def add_download(self, messages: Union[types.Message, List[types.Message]]):
        """Add messages to download queue."""
        if isinstance(messages, types.Message):
            messages = [messages]
        
        # Filter messages with media
        media_messages = [msg for msg in messages if msg.media]
        
        if not media_messages:
            QMessageBox.information(self, "No Media", "No downloadable media found in selected messages.")
            return
        
        # Add to download list
        for message in media_messages:
            filename = self.generate_filename(message)
            if filename not in self.download_items:
                item = DownloadItem(filename)
                self.download_items[filename] = item
                self.download_list.addItem(item)
        
        self.status_label.setText(f"{len(self.download_items)} files in queue")
        
        # Start download if not already running
        if not self.download_worker or not self.download_worker.isRunning():
            self.start_downloads(media_messages)
    
    def start_downloads(self, messages: List[types.Message]):
        """Start downloading files."""
        if not self.telegram_client.is_authenticated:
            QMessageBox.warning(self, "Not Authenticated", "Please login first.")
            return
        
        self.download_worker = DownloadWorker(
            self.telegram_client, 
            messages, 
            self.config.download_path
        )
        
        # Connect signals
        self.download_worker.download_progress.connect(self.on_download_progress)
        self.download_worker.download_completed.connect(self.on_download_completed)
        self.download_worker.download_failed.connect(self.on_download_failed)
        self.download_worker.all_downloads_completed.connect(self.on_all_downloads_completed)
        
        # Update UI state
        self.set_downloading_state(True)
        self.overall_progress.setVisible(True)
        self.overall_progress.setRange(0, len(messages))
        self.overall_progress.setValue(0)
        
        # Start worker
        self.download_worker.start()
        self.logger.info(f"Started downloading {len(messages)} files")
    
    def pause_downloads(self):
        """Pause downloads (placeholder - not implemented in worker yet)."""
        QMessageBox.information(self, "Not Implemented", "Pause functionality not yet implemented.")
    
    def cancel_downloads(self):
        """Cancel all downloads."""
        if self.download_worker and self.download_worker.isRunning():
            self.download_worker.cancel()
            self.download_worker.wait(3000)  # Wait up to 3 seconds
            
            if self.download_worker.isRunning():
                self.download_worker.terminate()
        
        self.set_downloading_state(False)
        self.status_label.setText("Downloads cancelled")
    
    def clear_downloads(self):
        """Clear the download list."""
        if self.download_worker and self.download_worker.isRunning():
            reply = QMessageBox.question(
                self, 
                "Clear Downloads", 
                "Downloads are in progress. Cancel and clear?",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                self.cancel_downloads()
            else:
                return
        
        self.download_list.clear()
        self.download_items.clear()
        self.overall_progress.setVisible(False)
        self.status_label.setText("No downloads")
    
    def on_download_progress(self, current: int, total: int, filename: str):
        """Handle download progress update."""
        if filename in self.download_items:
            self.download_items[filename].set_progress(current, total)
    
    def on_download_completed(self, filename: str, filepath: str):
        """Handle download completion."""
        if filename in self.download_items:
            self.download_items[filename].set_completed(filepath)
        
        # Update overall progress
        completed_count = sum(
            1 for item in self.download_items.values() 
            if item.status == "Completed"
        )
        self.overall_progress.setValue(completed_count)
        
        self.logger.info(f"Download completed: {filename}")
    
    def on_download_failed(self, filename: str, error: str):
        """Handle download failure."""
        if filename in self.download_items:
            self.download_items[filename].set_failed(error)
        
        self.logger.error(f"Download failed: {filename} - {error}")
    
    def on_all_downloads_completed(self):
        """Handle completion of all downloads."""
        self.set_downloading_state(False)
        
        completed_count = sum(
            1 for item in self.download_items.values() 
            if item.status == "Completed"
        )
        failed_count = sum(
            1 for item in self.download_items.values() 
            if item.status == "Failed"
        )
        
        self.status_label.setText(f"Downloads completed: {completed_count} successful, {failed_count} failed")
        
        if completed_count > 0:
            QMessageBox.information(
                self, 
                "Downloads Completed", 
                f"Successfully downloaded {completed_count} files to:\n{self.config.download_path}"
            )
    
    def generate_filename(self, message: types.Message) -> str:
        """Generate filename for a message (same logic as worker)."""
        timestamp = message.date.strftime("%Y%m%d_%H%M%S") if message.date else "unknown"
        
        if message.document and message.document.file_name:
            base_name = message.document.file_name
            name, ext = os.path.splitext(base_name)
            return f"{timestamp}_{name}{ext}"
        elif message.photo:
            return f"{timestamp}_photo.jpg"
        elif message.video:
            return f"{timestamp}_video.mp4"
        elif message.audio:
            return f"{timestamp}_audio.mp3"
        elif message.voice:
            return f"{timestamp}_voice.ogg"
        elif message.sticker:
            return f"{timestamp}_sticker.webp"
        elif message.animation:
            return f"{timestamp}_animation.gif"
        else:
            return f"{timestamp}_media"
    
    def set_downloading_state(self, downloading: bool):
        """Set the downloading state of the widget."""
        self.pause_button.setEnabled(downloading)
        self.cancel_button.setEnabled(downloading)
        self.browse_button.setEnabled(not downloading)
