"""Chat list widget for displaying Telegram chats, channels, and groups."""

import asyncio
import logging
from typing import List, Optional

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
    QLabel, QPushButton, QLineEdit, QComboBox, QProgressBar
)
from PySide6.QtCore import Qt, Signal, QThread
from PySide6.QtGui import QIcon, QPixmap

from pyrogram import types
from ..telegram.client import TelegramClient


class ChatLoadWorker(QThread):
    """Worker thread for loading chats."""
    
    # Signals
    chats_loaded = Signal(list)
    loading_failed = Signal(str)
    
    def __init__(self, client: TelegramClient):
        super().__init__()
        self.client = client
    
    def run(self):
        """Load chats from Telegram."""
        try:
            # Dispatch the coroutine to the authenticated client's loop if available
            if hasattr(self.client, "loop") and self.client.loop is not None:
                future = asyncio.run_coroutine_threadsafe(self.client.get_dialogs(), self.client.loop)
                dialogs = future.result()
            else:
                # Fallback: create a temporary loop in this thread
                loop = asyncio.new_event_loop()
                try:
                    asyncio.set_event_loop(loop)
                    dialogs = loop.run_until_complete(self.client.get_dialogs())
                finally:
                    loop.close()
            self.chats_loaded.emit(dialogs)
            
        except Exception as e:
            self.loading_failed.emit(str(e))


class ChatListItem(QListWidgetItem):
    """Custom list item for chat display."""
    
    def __init__(self, dialog: types.Dialog):
        super().__init__()
        self.dialog = dialog
        self.chat = dialog.chat
        
        # Set display text
        self.setText(self.get_display_name())
        
        # Set icon based on chat type
        self.setIcon(self.get_chat_icon())
        
        # Store additional data
        self.setData(Qt.UserRole, dialog)
    
    def get_display_name(self) -> str:
        """Get the display name for the chat."""
        chat = self.chat
        
        if hasattr(chat, 'title') and chat.title:
            return chat.title
        elif hasattr(chat, 'first_name'):
            name = chat.first_name
            if hasattr(chat, 'last_name') and chat.last_name:
                name += f" {chat.last_name}"
            return name
        elif hasattr(chat, 'username') and chat.username:
            return f"@{chat.username}"
        else:
            return f"Chat {chat.id}"
    
    def get_chat_icon(self) -> QIcon:
        """Get icon based on chat type."""
        # For now, return empty icon - could be enhanced with actual icons
        return QIcon()
    
    def get_chat_type_text(self) -> str:
        """Get human-readable chat type."""
        chat = self.chat
        
        if chat.type == types.ChatType.PRIVATE:
            return "Private"
        elif chat.type == types.ChatType.GROUP:
            return "Group"
        elif chat.type == types.ChatType.SUPERGROUP:
            return "Supergroup"
        elif chat.type == types.ChatType.CHANNEL:
            return "Channel"
        elif chat.type == types.ChatType.BOT:
            return "Bot"
        else:
            return "Unknown"


class ChatListWidget(QWidget):
    """Widget for displaying and managing the list of chats."""
    
    # Signals
    chat_selected = Signal(object)  # Emits the selected dialog
    
    def __init__(self, telegram_client: TelegramClient):
        super().__init__()
        self.telegram_client = telegram_client
        self.logger = logging.getLogger(__name__)
        self.load_worker: Optional[ChatLoadWorker] = None
        self.all_dialogs: List[types.Dialog] = []
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the widget UI."""
        layout = QVBoxLayout(self)
        
        # Header
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Chats")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        header_layout.addWidget(title_label)
        
        # Refresh button
        self.refresh_button = QPushButton("Refresh")
        self.refresh_button.clicked.connect(self.load_chats)
        header_layout.addWidget(self.refresh_button)
        
        layout.addLayout(header_layout)
        
        # Search and filter
        filter_layout = QVBoxLayout()
        
        # Search box
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Search chats...")
        self.search_edit.textChanged.connect(self.filter_chats)
        filter_layout.addWidget(self.search_edit)
        
        # Filter combo
        filter_combo_layout = QHBoxLayout()
        filter_combo_layout.addWidget(QLabel("Filter:"))
        
        self.filter_combo = QComboBox()
        self.filter_combo.addItems([
            "All", "Private", "Groups", "Supergroups", "Channels", "Bots"
        ])
        self.filter_combo.currentTextChanged.connect(self.filter_chats)
        filter_combo_layout.addWidget(self.filter_combo)
        
        filter_layout.addLayout(filter_combo_layout)
        layout.addLayout(filter_layout)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Chat list
        self.chat_list = QListWidget()
        self.chat_list.itemClicked.connect(self.on_chat_selected)
        layout.addWidget(self.chat_list)
        
        # Status label
        self.status_label = QLabel("No chats loaded")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: gray;")
        layout.addWidget(self.status_label)
    
    def load_chats(self):
        """Load chats from Telegram."""
        if not self.telegram_client.is_authenticated:
            self.status_label.setText("Not authenticated")
            return
        
        if self.load_worker and self.load_worker.isRunning():
            return  # Already loading
        
        self.set_loading_state(True)
        self.status_label.setText("Loading chats...")
        
        self.load_worker = ChatLoadWorker(self.telegram_client)
        self.load_worker.chats_loaded.connect(self.on_chats_loaded)
        self.load_worker.loading_failed.connect(self.on_loading_failed)
        self.load_worker.start()
    
    def on_chats_loaded(self, dialogs: List[types.Dialog]):
        """Handle loaded chats."""
        self.all_dialogs = dialogs
        self.populate_chat_list(dialogs)
        self.set_loading_state(False)
        self.status_label.setText(f"Loaded {len(dialogs)} chats")
        self.logger.info(f"Loaded {len(dialogs)} chats")
    
    def on_loading_failed(self, error: str):
        """Handle loading failure."""
        self.set_loading_state(False)
        self.status_label.setText(f"Failed to load chats: {error}")
        self.logger.error(f"Failed to load chats: {error}")
    
    def populate_chat_list(self, dialogs: List[types.Dialog]):
        """Populate the chat list with dialogs."""
        self.chat_list.clear()
        
        for dialog in dialogs:
            item = ChatListItem(dialog)
            self.chat_list.addItem(item)
    
    def filter_chats(self):
        """Filter chats based on search text and filter combo."""
        search_text = self.search_edit.text().lower()
        filter_type = self.filter_combo.currentText()
        
        filtered_dialogs = []
        
        for dialog in self.all_dialogs:
            chat = dialog.chat
            
            # Apply type filter
            if filter_type != "All":
                chat_type_text = ChatListItem(dialog).get_chat_type_text()
                if filter_type == "Groups" and chat_type_text not in ["Group", "Supergroup"]:
                    continue
                elif filter_type != "Groups" and chat_type_text != filter_type:
                    continue
            
            # Apply search filter
            if search_text:
                display_name = ChatListItem(dialog).get_display_name().lower()
                if search_text not in display_name:
                    continue
            
            filtered_dialogs.append(dialog)
        
        self.populate_chat_list(filtered_dialogs)
        self.status_label.setText(f"Showing {len(filtered_dialogs)} of {len(self.all_dialogs)} chats")
    
    def on_chat_selected(self, item: QListWidgetItem):
        """Handle chat selection."""
        if isinstance(item, ChatListItem):
            self.chat_selected.emit(item.dialog)
            self.logger.info(f"Selected chat: {item.get_display_name()}")
    
    def clear(self):
        """Clear the chat list."""
        self.chat_list.clear()
        self.all_dialogs.clear()
        self.status_label.setText("No chats loaded")
    
    def set_loading_state(self, loading: bool):
        """Set the loading state of the widget."""
        self.refresh_button.setEnabled(not loading)
        self.progress_bar.setVisible(loading)
        
        if loading:
            self.progress_bar.setRange(0, 0)  # Indeterminate progress
        else:
            self.progress_bar.setRange(0, 100)
            self.progress_bar.setValue(0)
