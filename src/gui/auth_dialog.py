"""Authentication dialog for Telegram login."""

import asyncio
import logging
from typing import Optional

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLineEdit, QPushButton, QLabel, QTabWidget, QWidget,
    QMessageBox, QProgressBar, QCheckBox, QInputDialog
)
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtGui import QFont

from ..core.config import Config
from ..telegram.client import TelegramClient


class AuthWorker(QThread):
    """Worker thread for authentication to avoid blocking the UI.
    Keeps the event loop alive after authentication so other threads can dispatch coroutines.
    """
    
    # Signals
    authentication_success = Signal()
    authentication_failed = Signal(str)
    phone_code_required = Signal(str)
    password_required = Signal()
    
    def __init__(self, client: TelegramClient, auth_type: str, **kwargs):
        super().__init__()
        self.client = client
        self.auth_type = auth_type
        self.kwargs = kwargs
        self.phone_code = None
        self.password = None
        self.loop: Optional[asyncio.AbstractEventLoop] = None
    
    def set_phone_code(self, code: str):
        """Set the phone verification code."""
        self.phone_code = code
    
    def set_password(self, password: str):
        """Set the 2FA password."""
        self.password = password
    
    def run(self):
        """Run the authentication process and keep the loop running for future tasks."""
        try:
            # Setup callbacks
            self.client.set_auth_callbacks(
                phone_code_callback=self.get_phone_code,
                password_callback=self.get_password
            )
            
            # Create and assign event loop for this thread
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            # Expose loop to the client so other threads can dispatch work to it
            setattr(self.client, "loop", self.loop)
            
            if self.auth_type == "user":
                success = self.loop.run_until_complete(
                    self.client.authenticate_user(self.kwargs["phone_number"])
                )
            elif self.auth_type == "bot":
                success = self.loop.run_until_complete(
                    self.client.authenticate_bot(self.kwargs["bot_token"])
                )
            else:
                raise ValueError(f"Unknown auth type: {self.auth_type}")
            
            if success:
                self.authentication_success.emit()
                # Keep loop alive so other threads can submit tasks to it
                self.loop.run_forever()
            else:
                self.authentication_failed.emit("Authentication failed")
                
        except Exception as e:
            self.authentication_failed.emit(str(e))
        finally:
            # Close the loop when the thread is finishing (after loop.stop is called elsewhere)
            if self.loop is not None and not self.loop.is_closed():
                try:
                    self.loop.close()
                except Exception:
                    pass
    
    def get_phone_code(self, phone_number: str) -> str:
        """Callback for phone code request."""
        self.phone_code_required.emit(phone_number)
        
        # Wait for code to be set
        while self.phone_code is None:
            self.msleep(100)
        
        code = self.phone_code
        self.phone_code = None
        return code
    
    def get_password(self) -> str:
        """Callback for 2FA password request."""
        self.password_required.emit()
        
        # Wait for password to be set
        while self.password is None:
            self.msleep(100)
        
        password = self.password
        self.password = None
        return password


class AuthDialog(QDialog):
    """Authentication dialog for Telegram login."""
    
    def __init__(self, config: Config, parent=None, telegram_client: Optional[TelegramClient] = None):
        super().__init__(parent)
        self.config = config
        # Reuse provided TelegramClient if available to share session/state with MainWindow
        self.telegram_client = telegram_client if telegram_client is not None else TelegramClient(config)
        self.auth_worker: Optional[AuthWorker] = None
        self.logger = logging.getLogger(__name__)
        
        self.setup_ui()
        self.load_saved_credentials()
    
    def setup_ui(self):
        """Setup the dialog UI."""
        self.setWindowTitle("Telegram Authentication")
        self.setModal(True)
        self.setFixedSize(400, 500)
        
        layout = QVBoxLayout(self)
        
        # Title
        title = QLabel("Login to Telegram")
        title.setAlignment(Qt.AlignCenter)
        font = QFont()
        font.setPointSize(16)
        font.setBold(True)
        title.setFont(font)
        layout.addWidget(title)
        
        # Tab widget for different auth methods
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # User authentication tab
        self.setup_user_auth_tab()
        
        # Bot authentication tab
        self.setup_bot_auth_tab()
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.login_button = QPushButton("Login")
        self.login_button.clicked.connect(self.start_authentication)
        button_layout.addWidget(self.login_button)
        
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
    
    def setup_user_auth_tab(self):
        """Setup the user authentication tab."""
        user_widget = QWidget()
        layout = QFormLayout(user_widget)
        
        # API credentials
        self.api_id_edit = QLineEdit()
        self.api_id_edit.setPlaceholderText("Your API ID from my.telegram.org")
        layout.addRow("API ID:", self.api_id_edit)
        
        self.api_hash_edit = QLineEdit()
        self.api_hash_edit.setPlaceholderText("Your API Hash from my.telegram.org")
        layout.addRow("API Hash:", self.api_hash_edit)
        
        # Phone number
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("+**********")
        layout.addRow("Phone Number:", self.phone_edit)
        
        # Save credentials checkbox
        self.save_credentials_check = QCheckBox("Save credentials")
        self.save_credentials_check.setChecked(True)
        layout.addRow("", self.save_credentials_check)
        
        self.tab_widget.addTab(user_widget, "User Account")
    
    def setup_bot_auth_tab(self):
        """Setup the bot authentication tab."""
        bot_widget = QWidget()
        layout = QFormLayout(bot_widget)
        
        # API credentials
        self.bot_api_id_edit = QLineEdit()
        self.bot_api_id_edit.setPlaceholderText("Your API ID from my.telegram.org")
        layout.addRow("API ID:", self.bot_api_id_edit)
        
        self.bot_api_hash_edit = QLineEdit()
        self.bot_api_hash_edit.setPlaceholderText("Your API Hash from my.telegram.org")
        layout.addRow("API Hash:", self.bot_api_hash_edit)
        
        # Bot token
        self.bot_token_edit = QLineEdit()
        self.bot_token_edit.setPlaceholderText("Bot token from @BotFather")
        layout.addRow("Bot Token:", self.bot_token_edit)
        
        # Save credentials checkbox
        self.save_bot_credentials_check = QCheckBox("Save credentials")
        self.save_bot_credentials_check.setChecked(True)
        layout.addRow("", self.save_bot_credentials_check)
        
        self.tab_widget.addTab(bot_widget, "Bot Account")
    
    def load_saved_credentials(self):
        """Load saved credentials from config."""
        if self.config.api_id:
            self.api_id_edit.setText(self.config.api_id)
            self.bot_api_id_edit.setText(self.config.api_id)
        
        if self.config.api_hash:
            self.api_hash_edit.setText(self.config.api_hash)
            self.bot_api_hash_edit.setText(self.config.api_hash)
        
        if self.config.bot_token:
            self.bot_token_edit.setText(self.config.bot_token)
    
    def start_authentication(self):
        """Start the authentication process."""
        current_tab = self.tab_widget.currentIndex()
        
        if current_tab == 0:  # User authentication
            self.authenticate_user()
        else:  # Bot authentication
            self.authenticate_bot()
    
    def authenticate_user(self):
        """Authenticate as user."""
        api_id = self.api_id_edit.text().strip()
        api_hash = self.api_hash_edit.text().strip()
        phone = self.phone_edit.text().strip()
        
        if not all([api_id, api_hash, phone]):
            QMessageBox.warning(self, "Error", "Please fill in all fields.")
            return
        
        # Save credentials if requested
        if self.save_credentials_check.isChecked():
            self.config.save_credentials(api_id, api_hash)
        
        # Update client config
        self.telegram_client.config.api_id = api_id
        self.telegram_client.config.api_hash = api_hash
        
        # Start authentication
        self.set_ui_state(False)
        self.status_label.setText("Connecting...")
        
        self.auth_worker = AuthWorker(
            self.telegram_client, 
            "user", 
            phone_number=phone
        )
        self.auth_worker.authentication_success.connect(self.on_auth_success)
        self.auth_worker.authentication_failed.connect(self.on_auth_failed)
        self.auth_worker.phone_code_required.connect(self.on_phone_code_required)
        self.auth_worker.password_required.connect(self.on_password_required)
        self.auth_worker.start()
    
    def authenticate_bot(self):
        """Authenticate as bot."""
        api_id = self.bot_api_id_edit.text().strip()
        api_hash = self.bot_api_hash_edit.text().strip()
        bot_token = self.bot_token_edit.text().strip()
        
        if not all([api_id, api_hash, bot_token]):
            QMessageBox.warning(self, "Error", "Please fill in all fields.")
            return
        
        # Save credentials if requested
        if self.save_bot_credentials_check.isChecked():
            self.config.save_credentials(api_id, api_hash, bot_token)
        
        # Update client config
        self.telegram_client.config.api_id = api_id
        self.telegram_client.config.api_hash = api_hash
        
        # Start authentication
        self.set_ui_state(False)
        self.status_label.setText("Connecting...")
        
        self.auth_worker = AuthWorker(
            self.telegram_client,
            "bot",
            bot_token=bot_token
        )
        self.auth_worker.authentication_success.connect(self.on_auth_success)
        self.auth_worker.authentication_failed.connect(self.on_auth_failed)
        self.auth_worker.start()
    
    def on_phone_code_required(self, phone_number: str):
        """Handle phone code request."""
        self.status_label.setText(f"Verification code sent to {phone_number}")
        
        code, ok = QInputDialog.getText(
            self,
            "Verification Code",
            f"Enter the verification code sent to {phone_number}:"
        )
        
        if ok and code:
            self.auth_worker.set_phone_code(code)
        else:
            self.auth_worker.terminate()
            self.on_auth_failed("Authentication cancelled")
    
    def on_password_required(self):
        """Handle 2FA password request."""
        self.status_label.setText("Two-factor authentication required")
        
        password, ok = QInputDialog.getText(
            self,
            "Two-Factor Authentication",
            "Enter your 2FA password:",
            QLineEdit.Password
        )
        
        if ok and password:
            self.auth_worker.set_password(password)
        else:
            self.auth_worker.terminate()
            self.on_auth_failed("Authentication cancelled")
    
    def on_auth_success(self):
        """Handle successful authentication."""
        self.status_label.setText("Authentication successful!")
        self.set_ui_state(True)
        QTimer.singleShot(1000, self.accept)
    
    def on_auth_failed(self, error: str):
        """Handle authentication failure."""
        self.status_label.setText(f"Authentication failed: {error}")
        self.set_ui_state(True)
        QMessageBox.critical(self, "Authentication Failed", error)
        # Ensure worker loop is stopped if running
        if self.auth_worker and self.auth_worker.isRunning() and getattr(self.auth_worker, "loop", None):
            try:
                self.auth_worker.loop.call_soon_threadsafe(self.auth_worker.loop.stop)
            except Exception:
                pass
    
    def set_ui_state(self, enabled: bool):
        """Enable/disable UI elements."""
        self.login_button.setEnabled(enabled)
        self.tab_widget.setEnabled(enabled)
        self.progress_bar.setVisible(not enabled)
        
        if not enabled:
            self.progress_bar.setRange(0, 0)  # Indeterminate progress
        else:
            self.progress_bar.setRange(0, 100)
            self.progress_bar.setValue(0)
    
    def closeEvent(self, event):
        """Handle dialog close event."""
        if self.auth_worker and self.auth_worker.isRunning():
            # Stop the worker's loop gracefully so submitted tasks can finish
            if getattr(self.auth_worker, "loop", None):
                try:
                    self.auth_worker.loop.call_soon_threadsafe(self.auth_worker.loop.stop)
                except Exception:
                    pass
            self.auth_worker.wait(2000)
        event.accept()
