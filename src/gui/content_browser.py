"""Content browser widget for displaying messages from selected chats."""

import asyncio
import logging
import os
import tempfile
from pathlib import Path
from typing import List, Optional
from datetime import datetime

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
    QLabel, QPushButton, QTextEdit, QSplitter, QProgressBar,
    QCheckBox, QSpinBox, QComboBox
)
from PySide6.QtCore import Qt, Signal, QThread, QUrl
from PySide6.QtGui import QFont, QPixmap, QMovie
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput

from pyrogram import types
from ..telegram.client import TelegramClient


class MessageLoadWorker(QThread):
    """Worker thread for loading messages."""
    
    # Signals
    messages_loaded = Signal(list)
    loading_failed = Signal(str)
    
    def __init__(self, client: TelegramClient, chat_id: int, limit: int = 100):
        super().__init__()
        self.client = client
        self.chat_id = chat_id
        self.limit = limit
    
    def run(self):
        """Load messages from the chat."""
        try:
            # Prefer using the client's loop to avoid cross-loop issues
            if hasattr(self.client, "loop") and self.client.loop is not None:
                future = asyncio.run_coroutine_threadsafe(
                    self.client.get_chat_history(self.chat_id, self.limit),
                    self.client.loop,
                )
                messages = future.result()
            else:
                loop = asyncio.new_event_loop()
                try:
                    asyncio.set_event_loop(loop)
                    messages = loop.run_until_complete(
                        self.client.get_chat_history(self.chat_id, self.limit)
                    )
                finally:
                    loop.close()
            self.messages_loaded.emit(messages)
            
        except Exception as e:
            self.loading_failed.emit(str(e))


class MessageListItem(QListWidgetItem):
    """Custom list item for message display."""
    
    def __init__(self, message: types.Message):
        super().__init__()
        self.message = message
        
        # Set display text
        self.setText(self.get_display_text())
        
        # Store message data
        self.setData(Qt.UserRole, message)
        
        # Set checkable for selection
        self.setFlags(self.flags() | Qt.ItemIsUserCheckable)
        self.setCheckState(Qt.Unchecked)
    
    def get_display_text(self) -> str:
        """Get display text for the message."""
        msg = self.message
        
        # Format timestamp
        if msg.date:
            timestamp = msg.date.strftime("%Y-%m-%d %H:%M:%S")
        else:
            timestamp = "Unknown"
        
        # Get sender info
        sender = "Unknown"
        if msg.from_user:
            if msg.from_user.first_name:
                sender = msg.from_user.first_name
                if msg.from_user.last_name:
                    sender += f" {msg.from_user.last_name}"
            elif msg.from_user.username:
                sender = f"@{msg.from_user.username}"
        
        # Get message content preview
        content = ""
        if msg.text:
            content = msg.text[:50] + ("..." if len(msg.text) > 50 else "")
        elif msg.media:
            content = f"[{self.get_media_type()}]"
        else:
            content = "[Empty message]"
        
        return f"[{timestamp}] {sender}: {content}"
    
    def get_media_type(self) -> str:
        """Get the type of media in the message."""
        msg = self.message
        
        if msg.photo:
            return "Photo"
        elif msg.video:
            return "Video"
        elif msg.audio:
            return "Audio"
        elif msg.voice:
            return "Voice"
        elif msg.document:
            return "Document"
        elif msg.sticker:
            return "Sticker"
        elif msg.animation:
            return "Animation"
        else:
            return "Media"
    
    def has_downloadable_media(self) -> bool:
        """Check if the message has downloadable media."""
        return bool(self.message.media)


class MediaPreviewWorker(QThread):
    """Worker to fetch media preview files without blocking UI."""
    preview_ready = Signal(str, str)  # (media_type, local_path)
    failed = Signal(str)

    def __init__(self, client: TelegramClient, message: types.Message, tmp_dir: Path):
        super().__init__()
        self.client = client
        self.message = message
        self.tmp_dir = tmp_dir

    def run(self):
        try:
            # Decide filename and download minimal media/thumbnail
            filename = self._temp_filename(self.message)
            dest = self.tmp_dir / filename

            async def _download():
                # Prefer a thumbnail when available
                if self.message.photo:
                    return await self.client.download_media(self.message, str(dest))
                if self.message.animation:
                    return await self.client.download_media(self.message, str(dest))
                if self.message.document and self.message.document.mime_type and self.message.document.mime_type.startswith("image/"):
                    return await self.client.download_media(self.message, str(dest))
                if self.message.audio or (self.message.document and self.message.document.mime_type and self.message.document.mime_type.startswith("audio/")):
                    return await self.client.download_media(self.message, str(dest))
                # Fallback: nothing downloadable for preview
                return None

            if hasattr(self.client, "loop") and self.client.loop is not None:
                fut = asyncio.run_coroutine_threadsafe(_download(), self.client.loop)
                local_path = fut.result()
            else:
                loop = asyncio.new_event_loop()
                try:
                    asyncio.set_event_loop(loop)
                    local_path = loop.run_until_complete(_download())
                finally:
                    loop.close()

            if not local_path:
                self.failed.emit("No preview available")
                return

            media_type = self._media_type(self.message)
            self.preview_ready.emit(media_type, local_path)
        except Exception as e:
            self.failed.emit(str(e))

    def _temp_filename(self, msg: types.Message) -> str:
        ts = msg.date.strftime("%Y%m%d_%H%M%S") if msg.date else "tmp"
        if msg.photo:
            return f"{ts}_preview.jpg"
        if msg.animation:
            return f"{ts}_preview.gif"
        if msg.audio:
            return f"{ts}_preview.mp3"
        if msg.document and msg.document.mime_type:
            if msg.document.mime_type.startswith("image/"):
                ext = msg.document.file_name.split(".")[-1] if msg.document.file_name else "img"
                return f"{ts}_preview.{ext}"
            if msg.document.mime_type.startswith("audio/"):
                ext = msg.document.file_name.split(".")[-1] if msg.document.file_name else "audio"
                return f"{ts}_preview.{ext}"
        return f"{ts}_preview.bin"

    def _media_type(self, msg: types.Message) -> str:
        if msg.animation:
            return "gif"
        if msg.photo or (msg.document and msg.document.mime_type and msg.document.mime_type.startswith("image/")):
            return "image"
        if msg.audio or (msg.document and msg.document.mime_type and msg.document.mime_type.startswith("audio/")):
            return "audio"
        return "other"


class ContentBrowserWidget(QWidget):
    """Widget for browsing content from selected chats."""
    
    # Signals
    download_requested = Signal(object)  # Emits message or list of messages
    
    def __init__(self, telegram_client: TelegramClient):
        super().__init__()
        self.telegram_client = telegram_client
        self.logger = logging.getLogger(__name__)
        self.load_worker: Optional[MessageLoadWorker] = None
        self.current_dialog: Optional[types.Dialog] = None
        self.all_messages: List[types.Message] = []
        self.preview_worker: Optional[MediaPreviewWorker] = None
        self.temp_dir = Path(tempfile.mkdtemp(prefix="tg_previews_"))
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the widget UI."""
        layout = QVBoxLayout(self)
        
        # Header
        header_layout = QHBoxLayout()
        
        self.chat_title_label = QLabel("No chat selected")
        self.chat_title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        header_layout.addWidget(self.chat_title_label)
        
        header_layout.addStretch()
        
        # Load more button
        self.load_more_button = QPushButton("Load More")
        self.load_more_button.clicked.connect(self.load_more_messages)
        self.load_more_button.setEnabled(False)
        header_layout.addWidget(self.load_more_button)
        
        layout.addLayout(header_layout)
        
        # Controls
        controls_layout = QHBoxLayout()
        
        # Message limit
        controls_layout.addWidget(QLabel("Limit:"))
        self.limit_spinbox = QSpinBox()
        self.limit_spinbox.setRange(10, 1000)
        self.limit_spinbox.setValue(100)
        self.limit_spinbox.setSuffix(" messages")
        controls_layout.addWidget(self.limit_spinbox)
        
        # Filter by media
        self.media_only_checkbox = QCheckBox("Media only")
        self.media_only_checkbox.toggled.connect(self.filter_messages)
        controls_layout.addWidget(self.media_only_checkbox)
        
        controls_layout.addStretch()
        
        # Selection controls
        self.select_all_button = QPushButton("Select All")
        self.select_all_button.clicked.connect(self.select_all_messages)
        controls_layout.addWidget(self.select_all_button)
        
        self.select_none_button = QPushButton("Select None")
        self.select_none_button.clicked.connect(self.select_no_messages)
        controls_layout.addWidget(self.select_none_button)
        
        layout.addLayout(controls_layout)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Splitter for messages and preview
        splitter = QSplitter(Qt.Vertical)
        layout.addWidget(splitter)
        
        # Message list
        self.message_list = QListWidget()
        self.message_list.itemClicked.connect(self.on_message_selected)
        splitter.addWidget(self.message_list)
        
        # Message preview
        preview_widget = QWidget()
        preview_layout = QVBoxLayout(preview_widget)
        
        preview_layout.addWidget(QLabel("Message Preview:"))
        self.message_preview = QTextEdit()
        self.message_preview.setReadOnly(True)
        self.message_preview.setMaximumHeight(120)
        preview_layout.addWidget(self.message_preview)

        # Media preview area
        self.media_label = QLabel("No media preview")
        self.media_label.setAlignment(Qt.AlignCenter)
        self.media_label.setMinimumHeight(180)
        self.media_label.setStyleSheet("border: 1px solid #444; background: #222; color: #bbb;")
        preview_layout.addWidget(self.media_label)

        # Audio player
        self.audio_player = QMediaPlayer()
        self.audio_output = QAudioOutput()
        self.audio_player.setAudioOutput(self.audio_output)
        self.audio_status = QLabel("")
        self.audio_status.setStyleSheet("color: #888;")
        preview_layout.addWidget(self.audio_status)
        
        splitter.addWidget(preview_widget)
        
        # Download controls
        download_layout = QHBoxLayout()
        
        self.download_selected_button = QPushButton("Download Selected")
        self.download_selected_button.clicked.connect(self.download_selected_messages)
        self.download_selected_button.setEnabled(False)
        download_layout.addWidget(self.download_selected_button)
        
        self.download_all_button = QPushButton("Download All Media")
        self.download_all_button.clicked.connect(self.download_all_media)
        self.download_all_button.setEnabled(False)
        download_layout.addWidget(self.download_all_button)
        
        download_layout.addStretch()
        
        layout.addLayout(download_layout)
        
        # Status label
        self.status_label = QLabel("No chat selected")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: gray;")
        layout.addWidget(self.status_label)
        
        # Set splitter proportions
        splitter.setSizes([400, 150])
    
    def load_chat(self, dialog: types.Dialog):
        """Load messages from the selected chat."""
        self.current_dialog = dialog
        chat_name = dialog.chat.title or "Unknown Chat"
        self.chat_title_label.setText(f"Chat: {chat_name}")
        
        self.load_messages()
    
    def load_messages(self):
        """Load messages from the current chat."""
        if not self.current_dialog or not self.telegram_client.is_authenticated:
            return
        
        if self.load_worker and self.load_worker.isRunning():
            return  # Already loading
        
        self.set_loading_state(True)
        self.status_label.setText("Loading messages...")
        
        limit = self.limit_spinbox.value()
        self.load_worker = MessageLoadWorker(
            self.telegram_client, 
            self.current_dialog.chat.id, 
            limit
        )
        self.load_worker.messages_loaded.connect(self.on_messages_loaded)
        self.load_worker.loading_failed.connect(self.on_loading_failed)
        self.load_worker.start()
    
    def load_more_messages(self):
        """Load more messages from the current chat."""
        # This would implement pagination - for now just reload with higher limit
        current_limit = self.limit_spinbox.value()
        self.limit_spinbox.setValue(min(current_limit + 100, 1000))
        self.load_messages()
    
    def on_messages_loaded(self, messages: List[types.Message]):
        """Handle loaded messages."""
        self.all_messages = messages
        self.populate_message_list(messages)
        self.set_loading_state(False)
        
        media_count = sum(1 for msg in messages if msg.media)
        self.status_label.setText(f"Loaded {len(messages)} messages ({media_count} with media)")
        self.logger.info(f"Loaded {len(messages)} messages from chat")
        
        # Enable controls
        self.load_more_button.setEnabled(True)
        self.download_all_button.setEnabled(media_count > 0)
    
    def on_loading_failed(self, error: str):
        """Handle loading failure."""
        self.set_loading_state(False)
        self.status_label.setText(f"Failed to load messages: {error}")
        self.logger.error(f"Failed to load messages: {error}")
    
    def populate_message_list(self, messages: List[types.Message]):
        """Populate the message list."""
        self.message_list.clear()
        
        for message in messages:
            item = MessageListItem(message)
            self.message_list.addItem(item)
    
    def filter_messages(self):
        """Filter messages based on current settings."""
        if not self.all_messages:
            return
        
        filtered_messages = self.all_messages
        
        if self.media_only_checkbox.isChecked():
            filtered_messages = [msg for msg in filtered_messages if msg.media]
        
        self.populate_message_list(filtered_messages)
        
        media_count = sum(1 for msg in filtered_messages if msg.media)
        self.status_label.setText(f"Showing {len(filtered_messages)} messages ({media_count} with media)")

    def on_preview_ready(self, media_type: str, local_path: str):
        """Render the preview based on media type and local file."""
        if media_type == "image":
            pix = QPixmap(local_path)
            if not pix.isNull():
                self.media_label.setPixmap(pix.scaled(
                    self.media_label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation
                ))
                self.media_label.setText("")
        elif media_type == "gif":
            movie = QMovie(local_path)
            self.media_label.setMovie(movie)
            movie.start()
            self.media_label.setText("")
        elif media_type == "audio":
            url = QUrl.fromLocalFile(local_path)
            self.audio_player.setSource(url)
            self.audio_player.play()
            base = os.path.basename(local_path)
            self.audio_status.setText(f"Playing: {base}")
        else:
            self.media_label.setText("No preview available")

    def on_preview_failed(self, error: str):
        self.media_label.setText(f"Preview error: {error}")
    
    def on_message_selected(self, item: QListWidgetItem):
        """Handle message selection for preview."""
        if isinstance(item, MessageListItem):
            message = item.message
            
            # Update text preview with rich details
            preview_lines = [
                f"Date: {message.date}",
            ]
            if message.from_user:
                sender_name = message.from_user.first_name or message.from_user.username or "Unknown"
                preview_lines.append(f"From: {sender_name}")
            if message.chat:
                preview_lines.append(f"Chat ID: {message.chat.id}")
            if message.text:
                preview_lines.append(f"Text: {message.text}")
            if message.document:
                preview_lines.append(f"File name: {message.document.file_name or 'Unknown'}")
                preview_lines.append(f"MIME: {message.document.mime_type or 'Unknown'}")
                preview_lines.append(f"Size: {message.document.file_size or 0} bytes")
            if message.photo and message.photo.sizes:
                sz = message.photo.sizes[-1]
                preview_lines.append(f"Photo size: {getattr(sz, 'w', '?')}x{getattr(sz, 'h', '?')}")
            if message.animation:
                preview_lines.append("Animation: GIF")
            if message.audio:
                preview_lines.append(f"Audio duration: {getattr(message.audio, 'duration', 0)}s")
                preview_lines.append(f"Title: {getattr(message.audio, 'title', '')}")
                preview_lines.append(f"Performer: {getattr(message.audio, 'performer', '')}")
            self.message_preview.setPlainText("\n".join(preview_lines))

            # Reset media preview
            self.media_label.setText("No media preview")
            self.media_label.setMovie(None)
            self.media_label.setPixmap(QPixmap())
            self.audio_player.stop()
            self.audio_status.setText("")

            # Start media preview worker if media exists
            if message.media:
                if self.preview_worker and self.preview_worker.isRunning():
                    self.preview_worker.terminate()
                self.preview_worker = MediaPreviewWorker(self.telegram_client, message, self.temp_dir)
                self.preview_worker.preview_ready.connect(self.on_preview_ready)
                self.preview_worker.failed.connect(self.on_preview_failed)
                self.preview_worker.start()
    
    def select_all_messages(self):
        """Select all messages."""
        for i in range(self.message_list.count()):
            item = self.message_list.item(i)
            item.setCheckState(Qt.Checked)
        self.update_download_button_state()
    
    def select_no_messages(self):
        """Deselect all messages."""
        for i in range(self.message_list.count()):
            item = self.message_list.item(i)
            item.setCheckState(Qt.Unchecked)
        self.update_download_button_state()
    
    def update_download_button_state(self):
        """Update the state of download buttons."""
        selected_count = sum(
            1 for i in range(self.message_list.count())
            if self.message_list.item(i).checkState() == Qt.Checked
        )
        self.download_selected_button.setEnabled(selected_count > 0)
    
    def download_selected_messages(self):
        """Download selected messages."""
        selected_messages = []
        for i in range(self.message_list.count()):
            item = self.message_list.item(i)
            if item.checkState() == Qt.Checked and isinstance(item, MessageListItem):
                selected_messages.append(item.message)
        
        if selected_messages:
            self.download_requested.emit(selected_messages)
    
    def download_all_media(self):
        """Download all media from current messages."""
        media_messages = [msg for msg in self.all_messages if msg.media]
        if media_messages:
            self.download_requested.emit(media_messages)
    
    def clear(self):
        """Clear the content browser."""
        self.message_list.clear()
        self.message_preview.clear()
        self.all_messages.clear()
        self.current_dialog = None
        self.chat_title_label.setText("No chat selected")
        self.status_label.setText("No chat selected")
        self.load_more_button.setEnabled(False)
        self.download_all_button.setEnabled(False)
        self.download_selected_button.setEnabled(False)
    
    def set_loading_state(self, loading: bool):
        """Set the loading state of the widget."""
        self.load_more_button.setEnabled(not loading and self.current_dialog is not None)
        self.progress_bar.setVisible(loading)
        
        if loading:
            self.progress_bar.setRange(0, 0)  # Indeterminate progress
        else:
            self.progress_bar.setRange(0, 100)
            self.progress_bar.setValue(0)
